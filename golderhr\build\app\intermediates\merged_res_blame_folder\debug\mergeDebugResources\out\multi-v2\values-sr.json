{"logs": [{"outputFile": "com.example.golderhr.app-mergeDebugResources-52:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\776702e8df955308735e19f57eaa0c87\\transformed\\biometric-1.1.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,259,380,514,645,772,903,1037,1137,1276,1409", "endColumns": "110,92,120,133,130,126,130,133,99,138,132,125", "endOffsets": "161,254,375,509,640,767,898,1032,1132,1271,1404,1530"}, "to": {"startLines": "54,57,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5729,6022,6513,6634,6768,6899,7026,7157,7291,7391,7530,7663", "endColumns": "110,92,120,133,130,126,130,133,99,138,132,125", "endOffsets": "5835,6110,6629,6763,6894,7021,7152,7286,7386,7525,7658,7784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\adc8160a94990199883d6d9f87c954dd\\transformed\\appcompat-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,8021", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,8103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b0c0c359288760136f5797fcc443a024\\transformed\\core-1.16.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "29,30,31,32,33,34,35,75", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2833,2931,3033,3130,3234,3338,3443,8108", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "2926,3028,3125,3229,3333,3438,3554,8204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6250b576436e3aba2e4229fd698d3169\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4552", "endColumns": "129", "endOffsets": "4677"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\05840cd26434a51e94c2f67f7d2b7d17\\transformed\\preference-1.2.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,343,495,664,750", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "171,258,338,490,659,745,827"}, "to": {"startLines": "55,58,72,73,76,77,78", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5840,6115,7789,7869,8209,8378,8464", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "5906,6197,7864,8016,8373,8459,8541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\da08fd731f7919b6d9fa1f8cb3c3a591\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,447,569,675,825,948,1056,1154,1299,1402,1558,1681,1826,1964,2028,2089", "endColumns": "101,151,121,105,149,122,107,97,144,102,155,122,144,137,63,60,75", "endOffsets": "294,446,568,674,824,947,1055,1153,1298,1401,1557,1680,1825,1963,2027,2088,2164"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3559,3665,3821,3947,4057,4211,4338,4450,4682,4831,4938,5098,5225,5374,5516,5584,5649", "endColumns": "105,155,125,109,153,126,111,101,148,106,159,126,148,141,67,64,79", "endOffsets": "3660,3816,3942,4052,4206,4333,4445,4547,4826,4933,5093,5220,5369,5511,5579,5644,5724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\43f536242e7d41c04ef9d5915cc2edb1\\transformed\\browser-1.8.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "56,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5911,6202,6302,6415", "endColumns": "110,99,112,97", "endOffsets": "6017,6297,6410,6508"}}]}]}