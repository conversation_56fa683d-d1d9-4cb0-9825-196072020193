<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="373fa269-47cb-4842-b8c3-69d19bc82246" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/lib/features/admin/data/models/admin_user_model.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/features/admin/data/models/admin_user_model.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/features/admin/data/repositories/admin_user_repository_impl.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/features/admin/data/repositories/admin_user_repository_impl.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/features/admin/domain/entities/admin_user_entity.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/features/admin/domain/entities/admin_user_entity.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/features/admin/presentation/widgets/admin_user_data_grid.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/features/admin/presentation/widgets/admin_user_data_grid.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/features/admin/presentation/widgets/admin_user_stats_widget.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/features/admin/presentation/widgets/admin_user_stats_widget.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/features/admin/presentation/widgets/edit_user_dialog.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/features/admin/presentation/widgets/edit_user_dialog.dart" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/features/more/presentation/pages/more_page.dart" beforeDir="false" afterPath="$PROJECT_DIR$/lib/features/more/presentation/pages/more_page.dart" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="FileEditorManager">
    <leaf>
      <file leaf-file-name="main.dart" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/lib/main.dart">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Dart File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;khathach699&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/khathach699/golden-hr.git&quot;,
    &quot;accountId&quot;: &quot;83f72cc7-4377-4ad0-a5b2-e6632fe9aafd&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/lib/features/auth/presentation/pages/login_page.dart" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2yMFAieiK5dyqrnOxZ6uEwM4scv" />
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1" />
    <panes>
      <pane id="ProjectPane">
        <option name="show-excluded-files" value="false" />
      </pane>
    </panes>
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Flutter.main.dart.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;com.google.services.firebase.aqiPopupShown&quot;: &quot;true&quot;,
    &quot;dart.analysis.tool.window.visible&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;io.flutter.reload.alreadyRun&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/Gordel_HR/golderhr/lib/features&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;editor.preferences.tabs&quot;,
    &quot;show.migrate.to.gradle.popup&quot;: &quot;false&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\Gordel_HR\golderhr\lib\features" />
      <recent name="E:\Gordel_HR\golderhr\lib\core\l10n" />
      <recent name="E:\flutter\golderhr\lib\features" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\Gordel_HR\golderhr\lib\features\attendance\presentation\cubit\attendance_page" />
      <recent name="E:\Gordel_HR\golderhr\lib\features\faceDetection\domain\entities" />
      <recent name="E:\Gordel_HR\golderhr\lib\features\more\presentation\pages" />
      <recent name="E:\Gordel_HR\golderhr\lib\shared\widgets" />
      <recent name="E:\Gordel_HR\golderhr\lib\features\home\presentation\pages" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="373fa269-47cb-4842-b8c3-69d19bc82246" name="Changes" comment="" />
      <created>1749636394432</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749636394432</updated>
    </task>
    <servers />
  </component>
  <component name="ToolWindowManager">
    <editor active="true" />
    <layout>
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="0" side_tool="false" content_ui="combo" />
    </layout>
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName="*.arb" />
  </component>
</project>