﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{2283835B-7877-3EF3-B1CD-E55E5213F63B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalProjectTargets", "ExternalProjectTargets", "{5FC89E91-7A41-3694-8BE1-614B6D284925}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "nuget-populate", "ExternalProjectTargets\nuget-populate", "{00399C70-D7F0-3BA4-B0D5-62487D822F44}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{1C484657-CEB2-3E4E-A573-9141F9E77F53}"
	ProjectSection(ProjectDependencies) = postProject
		{0B4F15CE-9208-3BFC-A2A4-25E19983F1DA} = {0B4F15CE-9208-3BFC-A2A4-25E19983F1DA}
		{6861E0AB-D831-37F0-8D5D-A709A781CBAA} = {6861E0AB-D831-37F0-8D5D-A709A781CBAA}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{0B4F15CE-9208-3BFC-A2A4-25E19983F1DA}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "nuget-populate", "nuget-populate.vcxproj", "{6861E0AB-D831-37F0-8D5D-A709A781CBAA}"
	ProjectSection(ProjectDependencies) = postProject
		{0B4F15CE-9208-3BFC-A2A4-25E19983F1DA} = {0B4F15CE-9208-3BFC-A2A4-25E19983F1DA}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1C484657-CEB2-3E4E-A573-9141F9E77F53}.Debug|x64.ActiveCfg = Debug|x64
		{0B4F15CE-9208-3BFC-A2A4-25E19983F1DA}.Debug|x64.ActiveCfg = Debug|x64
		{0B4F15CE-9208-3BFC-A2A4-25E19983F1DA}.Debug|x64.Build.0 = Debug|x64
		{6861E0AB-D831-37F0-8D5D-A709A781CBAA}.Debug|x64.ActiveCfg = Debug|x64
		{6861E0AB-D831-37F0-8D5D-A709A781CBAA}.Debug|x64.Build.0 = Debug|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{1C484657-CEB2-3E4E-A573-9141F9E77F53} = {2283835B-7877-3EF3-B1CD-E55E5213F63B}
		{0B4F15CE-9208-3BFC-A2A4-25E19983F1DA} = {2283835B-7877-3EF3-B1CD-E55E5213F63B}
		{00399C70-D7F0-3BA4-B0D5-62487D822F44} = {5FC89E91-7A41-3694-8BE1-614B6D284925}
		{6861E0AB-D831-37F0-8D5D-A709A781CBAA} = {00399C70-D7F0-3BA4-B0D5-62487D822F44}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {04BD1A4E-6815-3610-9CE8-D50DE756C302}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
