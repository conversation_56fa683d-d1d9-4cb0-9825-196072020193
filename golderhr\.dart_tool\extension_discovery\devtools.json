{"version": 2, "entries": [{"package": "provider", "rootUri": "file:///E:/flutter_pub_cache/hosted/pub.dev/provider-6.1.5/", "packageUri": "lib/", "config": {"name": "provider", "issueTracker": "https://github.com/rrousselGit/provider/issues", "version": "0.0.1", "materialIconCodePoint": "0xe0b1"}}, {"package": "shared_preferences", "rootUri": "file:///E:/flutter_pub_cache/hosted/pub.dev/shared_preferences-2.5.3/", "packageUri": "lib/", "config": {"name": "shared_preferences", "issueTracker": "https://github.com/flutter/flutter/issues?q=is%3Aissue+is%3Aopen+label%3A%22p%3A+shared_preferences%22", "version": "1.0.0", "materialIconCodePoint": "0xe683"}}, {"package": "gold<PERSON><PERSON>", "rootUri": "../", "packageUri": "lib/"}]}