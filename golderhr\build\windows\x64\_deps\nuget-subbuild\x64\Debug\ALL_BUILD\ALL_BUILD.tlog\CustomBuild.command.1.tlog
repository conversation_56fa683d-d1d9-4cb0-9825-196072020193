^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild -BE:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild --check-stamp-file E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
