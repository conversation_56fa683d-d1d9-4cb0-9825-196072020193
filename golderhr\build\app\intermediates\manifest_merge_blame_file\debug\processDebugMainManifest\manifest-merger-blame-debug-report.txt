1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.golderhr"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:3:5-67
15-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.CAMERA" />
16-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:2:5-65
16-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:2:22-62
17    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
17-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:4:5-79
17-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:4:22-76
18
19    <uses-feature android:name="android.hardware.camera" />
19-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:5:5-60
19-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:5:19-57
20
21    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
21-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:6:5-79
21-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:6:22-76
22    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
22-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:7:5-81
22-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:7:22-78
23    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
23-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:8:5-76
23-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:8:22-74
24    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
24-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:9:5-79
24-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:9:22-76
25    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
25-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:10:5-81
25-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:10:22-78
26    <uses-permission android:name="android.permission.WAKE_LOCK" />
26-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:11:5-68
26-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:11:22-65
27    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
27-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:12:5-71
27-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:12:22-69
28    <!--
29 Required to query activities that can process text, see:
30         https://developer.android.com/training/package-visibility and
31         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
32
33         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
34    -->
35    <queries>
35-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:50:5-84:15
36
37        <!-- Intent hiện có -->
38        <intent>
38-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:52:9-55:18
39            <action android:name="android.intent.action.PROCESS_TEXT" />
39-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:53:13-72
39-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:53:21-70
40
41            <data android:mimeType="text/plain" />
41-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:54:13-50
41-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:54:19-48
42        </intent>
43
44        <!-- ============================================= -->
45        <!-- BẮT ĐẦU PHẦN THÊM MỚI ĐỂ SỬA LỖI -->
46        <!-- ============================================= -->
47
48
49        <!-- Cho phép mở các link web (https) -->
50        <intent>
50-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:62:9-65:18
51            <action android:name="android.intent.action.VIEW" />
51-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:63:13-65
51-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:63:21-62
52
53            <data android:scheme="https" />
53-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:54:13-50
54        </intent>
55
56        <!-- Cho phép mở ứng dụng email (mailto) -->
57        <intent>
57-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:68:9-71:18
58            <action android:name="android.intent.action.SENDTO" />
58-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:69:13-67
58-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:69:21-64
59
60            <data android:scheme="mailto" />
60-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:54:13-50
61        </intent>
62
63        <!-- Cho phép mở ứng dụng gọi điện (tel) -->
64        <intent>
64-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:74:9-79:18
65
66            <!-- Sử dụng DIAL để mở trình quay số, không cần quyền. -->
67            <!-- Nếu muốn gọi thẳng, dùng ACTION_CALL và thêm quyền android.permission.CALL_PHONE -->
68            <action android:name="android.intent.action.DIAL" />
68-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:77:13-65
68-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:77:21-62
69
70            <data android:scheme="tel" />
70-->E:\Gordel_HR\golderhr\android\app\src\main\AndroidManifest.xml:54:13-50
71        </intent>
72
73        <!-- ============================================= -->
74        <!-- KẾT THÚC PHẦN THÊM MỚI -->
75        <!-- ============================================= -->
76    </queries>
77
78    <uses-permission android:name="android.permission.VIBRATE" /> <!-- suppress DeprecatedClassUsageInspection -->
78-->[:flutter_local_notifications] E:\Gordel_HR\golderhr\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
78-->[:flutter_local_notifications] E:\Gordel_HR\golderhr\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
79    <uses-permission android:name="android.permission.USE_FINGERPRINT" /> <!-- Required by older versions of Google Play services to create IID tokens -->
79-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\776702e8df955308735e19f57eaa0c87\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
79-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\776702e8df955308735e19f57eaa0c87\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
80    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
80-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
80-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
81
82    <permission
82-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0c0c359288760136f5797fcc443a024\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
83        android:name="com.example.golderhr.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
83-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0c0c359288760136f5797fcc443a024\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
84        android:protectionLevel="signature" />
84-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0c0c359288760136f5797fcc443a024\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
85
86    <uses-permission android:name="com.example.golderhr.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
86-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0c0c359288760136f5797fcc443a024\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
86-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0c0c359288760136f5797fcc443a024\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
87
88    <application
89        android:name="android.app.Application"
90        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
90-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0c0c359288760136f5797fcc443a024\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
91        android:debuggable="true"
92        android:extractNativeLibs="true"
93        android:icon="@mipmap/ic_launcher"
94        android:label="Golder HR" >
95        <activity
96            android:name="com.example.golderhr.MainActivity"
97            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
98            android:exported="true"
99            android:hardwareAccelerated="true"
100            android:launchMode="singleTop"
101            android:taskAffinity=""
102            android:theme="@style/LaunchTheme"
103            android:windowSoftInputMode="adjustResize" >
104
105            <!--
106                 Specifies an Android theme to apply to this Activity as soon as
107                 the Android process has started. This theme is visible to the user
108                 while the Flutter UI initializes. After that, this theme continues
109                 to determine the Window background behind the Flutter UI.
110            -->
111            <meta-data
112                android:name="io.flutter.embedding.android.NormalTheme"
113                android:resource="@style/NormalTheme" />
114
115            <intent-filter>
116                <action android:name="android.intent.action.MAIN" />
117
118                <category android:name="android.intent.category.LAUNCHER" />
119            </intent-filter>
120        </activity>
121        <!--
122             Don't delete the meta-data below.
123             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
124        -->
125        <meta-data
126            android:name="flutterEmbedding"
127            android:value="2" />
128
129        <service
129-->[:geolocator_android] E:\Gordel_HR\golderhr\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
130            android:name="com.baseflow.geolocator.GeolocatorLocationService"
130-->[:geolocator_android] E:\Gordel_HR\golderhr\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
131            android:enabled="true"
131-->[:geolocator_android] E:\Gordel_HR\golderhr\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
132            android:exported="false"
132-->[:geolocator_android] E:\Gordel_HR\golderhr\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
133            android:foregroundServiceType="location" />
133-->[:geolocator_android] E:\Gordel_HR\golderhr\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
134
135        <provider
135-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
136            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
136-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
137            android:authorities="com.example.golderhr.flutter.image_provider"
137-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
138            android:exported="false"
138-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
139            android:grantUriPermissions="true" >
139-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
140            <meta-data
140-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
141                android:name="android.support.FILE_PROVIDER_PATHS"
141-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
142                android:resource="@xml/flutter_image_picker_file_paths" />
142-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
143        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
144        <service
144-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
145            android:name="com.google.android.gms.metadata.ModuleDependencies"
145-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
146            android:enabled="false"
146-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
147            android:exported="false" >
147-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
148            <intent-filter>
148-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
149                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
149-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
149-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
150            </intent-filter>
151
152            <meta-data
152-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
153                android:name="photopicker_activity:0:required"
153-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
154                android:value="" />
154-->[:image_picker_android] E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
155        </service>
156
157        <activity
157-->[:url_launcher_android] E:\Gordel_HR\golderhr\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
158            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
158-->[:url_launcher_android] E:\Gordel_HR\golderhr\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
159            android:exported="false"
159-->[:url_launcher_android] E:\Gordel_HR\golderhr\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
160            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
160-->[:url_launcher_android] E:\Gordel_HR\golderhr\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
161
162        <service
162-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
163            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
163-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
164            android:exported="false"
164-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
165            android:permission="android.permission.BIND_JOB_SERVICE" />
165-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
166        <service
166-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
167            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
167-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
168            android:exported="false" >
168-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
169            <intent-filter>
169-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
170                <action android:name="com.google.firebase.MESSAGING_EVENT" />
170-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
170-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
171            </intent-filter>
172        </service>
173
174        <receiver
174-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
175            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
175-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
176            android:exported="true"
176-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
177            android:permission="com.google.android.c2dm.permission.SEND" >
177-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
178            <intent-filter>
178-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
179                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
179-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
179-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
180            </intent-filter>
181        </receiver>
182
183        <service
183-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
184            android:name="com.google.firebase.components.ComponentDiscoveryService"
184-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:18-89
185            android:directBootAware="true"
185-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
186            android:exported="false" >
186-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
187            <meta-data
187-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
188                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
188-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
189                android:value="com.google.firebase.components.ComponentRegistrar" />
189-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
190            <meta-data
190-->[:firebase_core] E:\Gordel_HR\golderhr\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
191                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
191-->[:firebase_core] E:\Gordel_HR\golderhr\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
192                android:value="com.google.firebase.components.ComponentRegistrar" />
192-->[:firebase_core] E:\Gordel_HR\golderhr\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
193            <meta-data
193-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
194                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
194-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
195                android:value="com.google.firebase.components.ComponentRegistrar" />
195-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
196            <meta-data
196-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
197                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
197-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
199            <meta-data
199-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eeaae32b0fa89aa38f29989197139b4\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
200                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
200-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eeaae32b0fa89aa38f29989197139b4\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
201                android:value="com.google.firebase.components.ComponentRegistrar" />
201-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eeaae32b0fa89aa38f29989197139b4\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
202            <meta-data
202-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eeaae32b0fa89aa38f29989197139b4\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
203                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
203-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eeaae32b0fa89aa38f29989197139b4\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7eeaae32b0fa89aa38f29989197139b4\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
205            <meta-data
205-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f6c3a36cd709297faeeb405e0f97f0c\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
206                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
206-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f6c3a36cd709297faeeb405e0f97f0c\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9f6c3a36cd709297faeeb405e0f97f0c\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
208            <meta-data
208-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
209                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
209-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
211            <meta-data
211-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70ae3badc0785fb84bbcebaccea9c92b\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
212                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
212-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70ae3badc0785fb84bbcebaccea9c92b\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
213                android:value="com.google.firebase.components.ComponentRegistrar" />
213-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70ae3badc0785fb84bbcebaccea9c92b\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
214        </service>
215
216        <provider
216-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
217            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
217-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
218            android:authorities="com.example.golderhr.flutterfirebasemessaginginitprovider"
218-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
219            android:exported="false"
219-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
220            android:initOrder="99" />
220-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
221
222        <service
222-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\85df54c19d834abdbbbd92dd84a53473\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:9:9-15:19
223            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
223-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\85df54c19d834abdbbbd92dd84a53473\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:10:13-91
224            android:directBootAware="true"
224-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1b9220f426b354c38473b6b6704705d\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
225            android:exported="false" >
225-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\85df54c19d834abdbbbd92dd84a53473\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:11:13-37
226            <meta-data
226-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\85df54c19d834abdbbbd92dd84a53473\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
227                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
227-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\85df54c19d834abdbbbd92dd84a53473\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
228                android:value="com.google.firebase.components.ComponentRegistrar" />
228-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\85df54c19d834abdbbbd92dd84a53473\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
229            <meta-data
229-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c77e6a5c93e7d7b029c8c0c7988ed2d\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
230                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
230-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c77e6a5c93e7d7b029c8c0c7988ed2d\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
231                android:value="com.google.firebase.components.ComponentRegistrar" />
231-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9c77e6a5c93e7d7b029c8c0c7988ed2d\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
232            <meta-data
232-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1b9220f426b354c38473b6b6704705d\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
233                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
233-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1b9220f426b354c38473b6b6704705d\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
234                android:value="com.google.firebase.components.ComponentRegistrar" />
234-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1b9220f426b354c38473b6b6704705d\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
235        </service>
236
237        <receiver
237-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
238            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
238-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
239            android:exported="true"
239-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
240            android:permission="com.google.android.c2dm.permission.SEND" >
240-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
241            <intent-filter>
241-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
242                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
242-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
242-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
243            </intent-filter>
244
245            <meta-data
245-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
246                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
246-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
247                android:value="true" />
247-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
248        </receiver>
249        <!--
250             FirebaseMessagingService performs security checks at runtime,
251             but set to not exported to explicitly avoid allowing another app to call it.
252        -->
253        <service
253-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
254            android:name="com.google.firebase.messaging.FirebaseMessagingService"
254-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
255            android:directBootAware="true"
255-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
256            android:exported="false" >
256-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7439e18a594d3da12b4f7b61da2cf2c2\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
257            <intent-filter android:priority="-500" >
257-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
258                <action android:name="com.google.firebase.MESSAGING_EVENT" />
258-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
258-->[:firebase_messaging] E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
259            </intent-filter>
260        </service>
261
262        <provider
262-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1b9220f426b354c38473b6b6704705d\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
263            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
263-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1b9220f426b354c38473b6b6704705d\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
264            android:authorities="com.example.golderhr.mlkitinitprovider"
264-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1b9220f426b354c38473b6b6704705d\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
265            android:exported="false"
265-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1b9220f426b354c38473b6b6704705d\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
266            android:initOrder="99" />
266-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a1b9220f426b354c38473b6b6704705d\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
267
268        <activity
268-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\da08fd731f7919b6d9fa1f8cb3c3a591\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
269            android:name="com.google.android.gms.common.api.GoogleApiActivity"
269-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\da08fd731f7919b6d9fa1f8cb3c3a591\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
270            android:exported="false"
270-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\da08fd731f7919b6d9fa1f8cb3c3a591\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
271            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
271-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\da08fd731f7919b6d9fa1f8cb3c3a591\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
272
273        <provider
273-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
274            android:name="com.google.firebase.provider.FirebaseInitProvider"
274-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
275            android:authorities="com.example.golderhr.firebaseinitprovider"
275-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
276            android:directBootAware="true"
276-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
277            android:exported="false"
277-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
278            android:initOrder="100" />
278-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\884eaa981a9fa4a373408d1fac13a2b7\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
279        <provider
279-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0f8f6cd1008fbbc847091c5fd8225b2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
280            android:name="androidx.startup.InitializationProvider"
280-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0f8f6cd1008fbbc847091c5fd8225b2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
281            android:authorities="com.example.golderhr.androidx-startup"
281-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0f8f6cd1008fbbc847091c5fd8225b2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
282            android:exported="false" >
282-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0f8f6cd1008fbbc847091c5fd8225b2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
283            <meta-data
283-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0f8f6cd1008fbbc847091c5fd8225b2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
284                android:name="androidx.emoji2.text.EmojiCompatInitializer"
284-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0f8f6cd1008fbbc847091c5fd8225b2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
285                android:value="androidx.startup" />
285-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0f8f6cd1008fbbc847091c5fd8225b2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
286            <meta-data
286-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
287                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
287-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
288                android:value="androidx.startup" />
288-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70db1f4abc282be2ad518e7752b359ea\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
289            <meta-data
289-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
290                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
290-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
291                android:value="androidx.startup" />
291-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
292        </provider>
293
294        <uses-library
294-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
295            android:name="androidx.window.extensions"
295-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
296            android:required="false" />
296-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
297        <uses-library
297-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
298            android:name="androidx.window.sidecar"
298-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
299            android:required="false" />
299-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\5519fbbac70289e20ed689cd5c7a916a\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
300
301        <meta-data
301-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6250b576436e3aba2e4229fd698d3169\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
302            android:name="com.google.android.gms.version"
302-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6250b576436e3aba2e4229fd698d3169\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
303            android:value="@integer/google_play_services_version" />
303-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6250b576436e3aba2e4229fd698d3169\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
304
305        <receiver
305-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
306            android:name="androidx.profileinstaller.ProfileInstallReceiver"
306-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
307            android:directBootAware="false"
307-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
308            android:enabled="true"
308-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
309            android:exported="true"
309-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
310            android:permission="android.permission.DUMP" >
310-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
311            <intent-filter>
311-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
312                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
312-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
313            </intent-filter>
314            <intent-filter>
314-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
315                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
316            </intent-filter>
317            <intent-filter>
317-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
318                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
318-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
319            </intent-filter>
320            <intent-filter>
320-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
321                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
321-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\0a4ce766192662351bcd27c214df34d0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
322            </intent-filter>
323        </receiver>
324
325        <service
325-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\01b4bdea3ac0c05d2b011556d8dc7fb4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
326            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
326-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\01b4bdea3ac0c05d2b011556d8dc7fb4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
327            android:exported="false" >
327-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\01b4bdea3ac0c05d2b011556d8dc7fb4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
328            <meta-data
328-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\01b4bdea3ac0c05d2b011556d8dc7fb4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
329                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
329-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\01b4bdea3ac0c05d2b011556d8dc7fb4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
330                android:value="cct" />
330-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\01b4bdea3ac0c05d2b011556d8dc7fb4\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
331        </service>
332        <service
332-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cafe25f1f5eae414dd1b066a95fe170\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
333            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
333-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cafe25f1f5eae414dd1b066a95fe170\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
334            android:exported="false"
334-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cafe25f1f5eae414dd1b066a95fe170\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
335            android:permission="android.permission.BIND_JOB_SERVICE" >
335-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cafe25f1f5eae414dd1b066a95fe170\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
336        </service>
337
338        <receiver
338-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cafe25f1f5eae414dd1b066a95fe170\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
339            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
339-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cafe25f1f5eae414dd1b066a95fe170\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
340            android:exported="false" />
340-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cafe25f1f5eae414dd1b066a95fe170\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
341    </application>
342
343</manifest>
