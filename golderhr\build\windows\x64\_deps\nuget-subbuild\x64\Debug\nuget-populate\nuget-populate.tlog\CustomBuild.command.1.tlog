^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\2E824E0C09E05F7498BA2A048BD61978\NUGET-POPULATE-MKDIR.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/tmp/nuget-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\2E824E0C09E05F7498BA2A048BD61978\NUGET-POPULATE-DOWNLOAD.RULE
setlocal
cd E:\Gordel_HR\golderhr\build\windows\x64\_deps
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/download-nuget-populate.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/verify-nuget-populate.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\2E824E0C09E05F7498BA2A048BD61978\NUGET-POPULATE-UPDATE.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-update
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\2E824E0C09E05F7498BA2A048BD61978\NUGET-POPULATE-PATCH.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\2E824E0C09E05F7498BA2A048BD61978\NUGET-POPULATE-COPYFILE.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E copy_if_different E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget.exe E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-src
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-copyfile
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\2E824E0C09E05F7498BA2A048BD61978\NUGET-POPULATE-CONFIGURE.RULE
setlocal
cd E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\2E824E0C09E05F7498BA2A048BD61978\NUGET-POPULATE-BUILD.RULE
setlocal
cd E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\2E824E0C09E05F7498BA2A048BD61978\NUGET-POPULATE-INSTALL.RULE
setlocal
cd E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\2E824E0C09E05F7498BA2A048BD61978\NUGET-POPULATE-TEST.RULE
setlocal
cd E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\5C0EE3AD85141572F76B47797C251667\NUGET-POPULATE-COMPLETE.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/Debug/nuget-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\C287BE55502C7189BBD304F8E4D78DF4\NUGET-POPULATE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild -BE:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild --check-stamp-file E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
