-- Merging decision tree log ---
manifest
ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:1:1-26:12
INJECTED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:1:1-26:12
	package
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:3:5-45
		INJECTED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:2:5-51
	xmlns:android
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:1:11-69
application
ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:5:5-25:19
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:6:9-14:20
	android:grantUriPermissions
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:10:13-47
	android:authorities
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:8:13-74
	android:exported
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:9:13-37
	android:name
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:7:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:11:13-13:75
	android:resource
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:13:17-72
	android:name
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:12:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:16:9-24:19
	android:enabled
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:17:17-40
	android:exported
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:18:17-41
	tools:ignore
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:19:17-44
	android:name
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:16:18-83
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:20:13-22:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:21:17-94
	android:name
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:21:25-91
meta-data#photopicker_activity:0:required
ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:23:13-90
	android:value
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:23:71-87
	android:name
		ADDED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml:23:24-70
uses-sdk
INJECTED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml
INJECTED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\flutter_pub_cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\src\main\AndroidManifest.xml
