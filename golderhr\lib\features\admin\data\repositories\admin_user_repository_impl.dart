import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';
import '../../domain/entities/admin_user_entity.dart';
import '../../domain/repositories/admin_user_repository.dart';
import '../datasources/admin_user_remote_data_source.dart';

class AdminUserRepositoryImpl implements AdminUserRepository {
  final AdminUserRemoteDataSource remoteDataSource;

  AdminUserRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, AdminUserListResult>> getAllUsers({
    int page = 1,
    int limit = 10,
    String? search,
    String? role,
    String? department,
    bool includeDeleted = false,
    String sortBy = 'createdAt',
    String sortOrder = 'desc',
  }) async {
    try {
      final response = await remoteDataSource.getAllUsers(
        page: page,
        limit: limit,
        search: search,
        role: role,
        department: department,
        includeDeleted: includeDeleted,
        sortBy: sortBy,
        sortOrder: sortOrder,
      );

      return Right(
        AdminUserListResult(
          users: response.users,
          pagination: UserPagination(
            currentPage: response.pagination.currentPage,
            totalPages: response.pagination.totalPages,
            totalUsers: response.pagination.totalUsers,
            hasNext: response.pagination.hasNext,
            hasPrev: response.pagination.hasPrev,
          ),
        ),
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, AdminUserEntity>> getUserById(String userId) async {
    try {
      final user = await remoteDataSource.getUserById(userId);
      return Right(user);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, AdminUserEntity>> createUser({
    required String fullname,
    required String email,
    required String password,
    String? phone,
    String? department,
    String? position,
    required String role,
    String? organization,
  }) async {
    try {
      final user = await remoteDataSource.createUser(
        fullname: fullname,
        email: email,
        password: password,
        phone: phone,
        department: department,
        position: position,
        role: role,
        organization: organization,
      );
      return Right(user);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, AdminUserEntity>> updateUser(
    String userId, {
    String? fullname,
    String? email,
    String? phone,
    String? department,
    String? position,
    String? role,
    String? organization,
    bool? isdisable,
  }) async {
    try {
      final user = await remoteDataSource.updateUser(
        userId,
        fullname: fullname,
        email: email,
        phone: phone,
        department: department,
        position: position,
        role: role,
        organization: organization,
        isdisable: isdisable,
      );
      return Right(user);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, AdminUserEntity>> softDeleteUser(String userId) async {
    try {
      final user = await remoteDataSource.softDeleteUser(userId);
      return Right(user);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, AdminUserEntity>> restoreUser(String userId) async {
    try {
      final user = await remoteDataSource.restoreUser(userId);
      return Right(user);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, AdminUserEntity>> toggleUserStatus(
    String userId,
  ) async {
    try {
      final user = await remoteDataSource.toggleUserStatus(userId);
      return Right(user);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, void>> resetUserPassword(
    String userId,
    String newPassword,
  ) async {
    try {
      await remoteDataSource.resetUserPassword(userId, newPassword);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, UserStatistics>> getUserStatistics() async {
    try {
      final statistics = await remoteDataSource.getUserStatistics();
      return Right(
        UserStatistics(
          overview: UserOverview(
            totalUsers: statistics.overview.totalUsers,
            activeUsers: statistics.overview.activeUsers,
            disabledUsers: statistics.overview.disabledUsers,
            deletedUsers: statistics.overview.deletedUsers,
          ),
          roleDistribution: statistics.roleDistribution
              .map(
                (role) => RoleDistributionEntity(
                  roleName: role.roleName,
                  count: role.count,
                ),
              )
              .toList(),
          departmentDistribution: statistics.departmentDistribution
              .map(
                (dept) => DepartmentDistributionEntity(
                  departmentName: dept.departmentName,
                  count: dept.count,
                ),
              )
              .toList(),
        ),
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, BulkOperationResult>> bulkDeleteUsers(
    List<String> userIds,
  ) async {
    try {
      final result = await remoteDataSource.bulkDeleteUsers(userIds);
      return Right(
        BulkOperationResult(
          message: result['message'] ?? 'Users deleted successfully',
          affectedCount: result['deletedCount'] ?? 0,
        ),
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, BulkOperationResult>> bulkRestoreUsers(
    List<String> userIds,
  ) async {
    try {
      final result = await remoteDataSource.bulkRestoreUsers(userIds);
      return Right(
        BulkOperationResult(
          message: result['message'] ?? 'Users restored successfully',
          affectedCount: result['restoredCount'] ?? 0,
        ),
      );
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }
}
