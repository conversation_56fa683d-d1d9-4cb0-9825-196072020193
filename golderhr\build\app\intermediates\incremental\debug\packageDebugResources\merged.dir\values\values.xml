<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="gcm_defaultSenderId" translatable="false">895106658180</string>
    <string name="google_api_key" translatable="false">AIzaSyBye5nMBw14Pmxt9oa2hYwHdCD3ts75fdw</string>
    <string name="google_app_id" translatable="false">1:895106658180:android:914f5c61af0e85b6f5f7d1</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBye5nMBw14Pmxt9oa2hYwHdCD3ts75fdw</string>
    <string name="google_storage_bucket" translatable="false">goldenhr-54cda.firebasestorage.app</string>
    <string name="project_id" translatable="false">goldenhr-54cda</string>
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
</resources>