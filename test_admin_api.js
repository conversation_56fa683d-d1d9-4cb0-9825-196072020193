const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const BASE_URL = 'http://localhost:3000/api';

// Test admin API
async function testAdminAPI() {
  try {
    console.log('🔍 Testing Admin API...\n');

    // 1. Login as admin
    console.log('1. Logging in as admin...');
    const loginCmd = `curl -s -X POST "${BASE_URL}/auth/login" -H "Content-Type: application/json" -d "{\\"email\\":\\"<EMAIL>\\",\\"password\\":\\"Admin123!\\"}"`;
    const { stdout: loginOutput } = await execAsync(loginCmd);
    const loginResponse = JSON.parse(loginOutput);

    if (!loginResponse.success) {
      throw new Error('Login failed: ' + loginResponse.message);
    }

    const token = loginResponse.data.token;
    const adminUser = loginResponse.data.user;
    console.log('✅ Login successful');
    console.log('Admin user:', {
      id: adminUser.id,
      email: adminUser.email,
      fullname: adminUser.fullname,
      role: adminUser.role
    });

    // 2. Test get all users
    console.log('\n2. Getting all users...');
    const usersCmd = `curl -s -X GET "${BASE_URL}/admin/users?page=1&limit=20" -H "Authorization: Bearer ${token}" -H "Content-Type: application/json"`;
    const { stdout: usersOutput } = await execAsync(usersCmd);
    const usersResponse = JSON.parse(usersOutput);

    if (!usersResponse.success) {
      throw new Error('Get users failed: ' + usersResponse.message);
    }

    const users = usersResponse.data.users;
    const pagination = usersResponse.data.pagination;
    
    console.log('✅ Get users successful');
    console.log('Total users in response:', users.length);
    console.log('Pagination:', pagination);
    
    // Check if admin user is in the list
    const adminInList = users.find(user => user.email === '<EMAIL>');
    console.log('Admin user in list:', adminInList ? '✅ YES' : '❌ NO');
    
    if (adminInList) {
      console.log('Admin user details:', {
        id: adminInList._id,
        fullname: adminInList.fullname,
        email: adminInList.email,
        role: adminInList.role,
        department: adminInList.department,
        isdeleted: adminInList.isdeleted,
        isdisable: adminInList.isdisable
      });
    }

    // 3. Test statistics
    console.log('\n3. Getting user statistics...');
    const statsCmd = `curl -s -X GET "${BASE_URL}/admin/users/statistics" -H "Authorization: Bearer ${token}" -H "Content-Type: application/json"`;
    const { stdout: statsOutput } = await execAsync(statsCmd);
    const statsResponse = JSON.parse(statsOutput);

    if (!statsResponse.success) {
      throw new Error('Get statistics failed: ' + statsResponse.message);
    }

    const stats = statsResponse.data;
    console.log('✅ Get statistics successful');
    console.log('Full stats response:', JSON.stringify(stats, null, 2));

    if (stats.overview) {
      console.log('Statistics:', {
        totalUsers: stats.overview.totalUsers,
        activeUsers: stats.overview.activeUsers,
        disabledUsers: stats.overview.disabledUsers,
        deletedUsers: stats.overview.deletedUsers
      });
      console.log('Role distribution:', stats.roleDistribution);
      console.log('Department distribution:', stats.departmentDistribution);
    } else {
      console.log('❌ Statistics format issue - no overview found');
    }

    // 4. Test with different filters
    console.log('\n4. Testing filters...');

    // Filter by role
    const roleFilterCmd = `curl -s -X GET "${BASE_URL}/admin/users?role=admin" -H "Authorization: Bearer ${token}" -H "Content-Type: application/json"`;
    const { stdout: roleFilterOutput } = await execAsync(roleFilterCmd);
    const roleFilterResponse = JSON.parse(roleFilterOutput);
    console.log('Users with admin role:', roleFilterResponse.data.users.length);

    // Filter by search
    const searchCmd = `curl -s -X GET "${BASE_URL}/admin/users?search=admin" -H "Authorization: Bearer ${token}" -H "Content-Type: application/json"`;
    const { stdout: searchOutput } = await execAsync(searchCmd);
    const searchResponse = JSON.parse(searchOutput);
    console.log('Users matching "admin" search:', searchResponse.data.users.length);

    // 5. Check disabled users specifically
    console.log('\n5. Checking disabled users...');
    const disabledUsers = users.filter(user => user.isdisable && !user.isdeleted);
    console.log('Disabled users count:', disabledUsers.length);
    console.log('Disabled users details:');
    disabledUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.fullname} (${user.email}) - disabled: ${user.isdisable}, deleted: ${user.isdeleted}`);
    });

    // 6. Check all users status
    console.log('\n6. All users status breakdown:');
    const activeUsers = users.filter(user => !user.isdisable && !user.isdeleted);
    const deletedUsers = users.filter(user => user.isdeleted);
    console.log(`Active: ${activeUsers.length}, Disabled: ${disabledUsers.length}, Deleted: ${deletedUsers.length}, Total: ${users.length}`);

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testAdminAPI();
