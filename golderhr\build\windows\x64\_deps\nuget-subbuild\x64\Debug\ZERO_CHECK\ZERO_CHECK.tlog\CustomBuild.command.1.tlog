^E:\GORDEL_HR\GOLDERHR\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\C287BE55502C7189BBD304F8E4D78DF4\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild -BE:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/Gordel_HR/golderhr/build/windows/x64/_deps/nuget-subbuild/nuget-populate.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
