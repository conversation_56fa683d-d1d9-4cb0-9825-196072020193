import 'package:equatable/equatable.dart';

class UserRole extends Equatable {
  final String id;
  final String name;

  const UserRole({required this.id, required this.name});

  factory UserRole.fromJson(Map<String, dynamic> json) {
    return UserRole(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'_id': id, 'name': name};
  }

  @override
  List<Object?> get props => [id, name];
}

class UserOrganization extends Equatable {
  final String id;
  final String name;

  const UserOrganization({required this.id, required this.name});

  factory UserOrganization.fromJson(Map<String, dynamic> json) {
    return UserOrganization(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'_id': id, 'name': name};
  }

  @override
  List<Object?> get props => [id, name];
}

class AdminUserEntity extends Equatable {
  final String id;
  final String fullname;
  final String email;
  final String? avatar;
  final String? phone;
  final String? department;
  final String? position;
  final double point;
  final bool isdisable;
  final UserRole role;
  final UserOrganization? organization;
  final bool isdeleted;
  final int? idMapper;
  final String? codeMapper;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AdminUserEntity({
    required this.id,
    required this.fullname,
    required this.email,
    this.avatar,
    this.phone,
    this.department,
    this.position,
    this.point = 0.0,
    required this.isdisable,
    required this.role,
    this.organization,
    required this.isdeleted,
    this.idMapper,
    this.codeMapper,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
    id,
    fullname,
    email,
    avatar,
    phone,
    department,
    position,
    point,
    isdisable,
    role,
    organization,
    isdeleted,
    idMapper,
    codeMapper,
    createdAt,
    updatedAt,
  ];

  // Helper getters
  bool get isActive => !isdisable && !isdeleted;
  bool get isAdmin => role.name.toLowerCase() == 'admin';
  bool get isManager => role.name.toLowerCase() == 'manager';
  bool get isHR => role.name.toLowerCase() == 'hr';

  String get statusText {
    if (isdeleted) return 'Deleted';
    if (isdisable) return 'Disabled';
    return 'Active';
  }

  String get displayName => fullname.isNotEmpty ? fullname : email;
  String get initials {
    final names = fullname.split(' ');
    if (names.length >= 2) {
      return '${names.first[0]}${names.last[0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names.first[0].toUpperCase();
    }
    return email[0].toUpperCase();
  }
}

class AdminUserListResult extends Equatable {
  final List<AdminUserEntity> users;
  final UserPagination pagination;

  const AdminUserListResult({required this.users, required this.pagination});

  @override
  List<Object?> get props => [users, pagination];
}

class UserPagination extends Equatable {
  final int currentPage;
  final int totalPages;
  final int totalUsers;
  final bool hasNext;
  final bool hasPrev;

  const UserPagination({
    required this.currentPage,
    required this.totalPages,
    required this.totalUsers,
    required this.hasNext,
    required this.hasPrev,
  });

  @override
  List<Object?> get props => [
    currentPage,
    totalPages,
    totalUsers,
    hasNext,
    hasPrev,
  ];
}

class UserStatistics extends Equatable {
  final UserOverview overview;
  final List<RoleDistributionEntity> roleDistribution;
  final List<DepartmentDistributionEntity> departmentDistribution;

  const UserStatistics({
    required this.overview,
    required this.roleDistribution,
    required this.departmentDistribution,
  });

  @override
  List<Object?> get props => [
    overview,
    roleDistribution,
    departmentDistribution,
  ];
}

class UserOverview extends Equatable {
  final int totalUsers;
  final int activeUsers;
  final int disabledUsers;
  final int deletedUsers;

  const UserOverview({
    required this.totalUsers,
    required this.activeUsers,
    required this.disabledUsers,
    required this.deletedUsers,
  });

  @override
  List<Object?> get props => [
    totalUsers,
    activeUsers,
    disabledUsers,
    deletedUsers,
  ];

  double get activePercentage =>
      totalUsers > 0 ? (activeUsers / totalUsers) * 100 : 0;
  double get disabledPercentage =>
      totalUsers > 0 ? (disabledUsers / totalUsers) * 100 : 0;
  double get deletedPercentage =>
      totalUsers > 0 ? (deletedUsers / totalUsers) * 100 : 0;
}

class RoleDistributionEntity extends Equatable {
  final String roleName;
  final int count;

  const RoleDistributionEntity({required this.roleName, required this.count});

  @override
  List<Object?> get props => [roleName, count];

  String get displayRoleName {
    switch (roleName.toLowerCase()) {
      case 'admin':
        return 'Administrator';
      case 'hr':
        return 'Human Resources';
      case 'manager':
        return 'Manager';
      case 'user':
        return 'Employee';
      default:
        return roleName;
    }
  }
}

class DepartmentDistributionEntity extends Equatable {
  final String departmentName;
  final int count;

  const DepartmentDistributionEntity({
    required this.departmentName,
    required this.count,
  });

  @override
  List<Object?> get props => [departmentName, count];

  String get displayDepartmentName {
    return departmentName.isEmpty ? 'No Department' : departmentName;
  }
}

class BulkOperationResult extends Equatable {
  final String message;
  final int affectedCount;

  const BulkOperationResult({
    required this.message,
    required this.affectedCount,
  });

  @override
  List<Object?> get props => [message, affectedCount];
}

// Filter and sort options
enum UserSortField {
  fullname,
  email,
  department,
  position,
  createdAt,
  updatedAt,
}

enum SortOrder { asc, desc }

enum UserRoleEnum { admin, hr, manager, user }

enum UserStatus { all, active, disabled, deleted }

class UserFilterOptions extends Equatable {
  final String? search;
  final String? role;
  final String? department;
  final UserStatus status;
  final UserSortField sortBy;
  final SortOrder sortOrder;
  final bool includeDeleted;

  const UserFilterOptions({
    this.search,
    this.role,
    this.department,
    this.status = UserStatus.all,
    this.sortBy = UserSortField.createdAt,
    this.sortOrder = SortOrder.desc,
    this.includeDeleted = false,
  });

  @override
  List<Object?> get props => [
    search,
    role,
    department,
    status,
    sortBy,
    sortOrder,
    includeDeleted,
  ];

  UserFilterOptions copyWith({
    String? search,
    String? role,
    String? department,
    UserStatus? status,
    UserSortField? sortBy,
    SortOrder? sortOrder,
    bool? includeDeleted,
    bool clearSearch = false,
    bool clearRole = false,
    bool clearDepartment = false,
  }) {
    return UserFilterOptions(
      search: clearSearch ? null : (search ?? this.search),
      role: clearRole ? null : (role ?? this.role),
      department: clearDepartment ? null : (department ?? this.department),
      status: status ?? this.status,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      includeDeleted: includeDeleted ?? this.includeDeleted,
    );
  }

  Map<String, dynamic> toQueryParams() {
    return {
      if (search != null && search!.isNotEmpty) 'search': search,
      if (role != null && role!.isNotEmpty) 'role': role,
      if (department != null && department!.isNotEmpty)
        'department': department,
      'includeDeleted': includeDeleted,
      'sortBy': sortBy.name,
      'sortOrder': sortOrder.name,
    };
  }
}

extension UserRoleEnumExtension on UserRoleEnum {
  String get displayName {
    switch (this) {
      case UserRoleEnum.admin:
        return 'Administrator';
      case UserRoleEnum.hr:
        return 'Human Resources';
      case UserRoleEnum.manager:
        return 'Manager';
      case UserRoleEnum.user:
        return 'Employee';
    }
  }

  String get value {
    return name;
  }
}

extension UserStatusExtension on UserStatus {
  String get displayName {
    switch (this) {
      case UserStatus.all:
        return 'All Users';
      case UserStatus.active:
        return 'Active';
      case UserStatus.disabled:
        return 'Disabled';
      case UserStatus.deleted:
        return 'Deleted';
    }
  }
}

extension UserSortFieldExtension on UserSortField {
  String get displayName {
    switch (this) {
      case UserSortField.fullname:
        return 'Name';
      case UserSortField.email:
        return 'Email';
      case UserSortField.department:
        return 'Department';
      case UserSortField.position:
        return 'Position';
      case UserSortField.createdAt:
        return 'Created Date';
      case UserSortField.updatedAt:
        return 'Updated Date';
    }
  }
}
