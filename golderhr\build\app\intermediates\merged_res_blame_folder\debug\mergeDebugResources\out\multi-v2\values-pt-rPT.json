{"logs": [{"outputFile": "com.example.golderhr.app-mergeDebugResources-52:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\776702e8df955308735e19f57eaa0c87\\transformed\\biometric-1.1.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,266,395,534,681,816,945,1092,1194,1334,1483", "endColumns": "115,94,128,138,146,134,128,146,101,139,148,125", "endOffsets": "166,261,390,529,676,811,940,1087,1189,1329,1478,1604"}, "to": {"startLines": "54,57,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5902,6204,6701,6830,6969,7116,7251,7380,7527,7629,7769,7918", "endColumns": "115,94,128,138,146,134,128,146,101,139,148,125", "endOffsets": "6013,6294,6825,6964,7111,7246,7375,7522,7624,7764,7913,8039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b0c0c359288760136f5797fcc443a024\\transformed\\core-1.16.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "29,30,31,32,33,34,35,75", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2836,2933,3035,3134,3234,3341,3447,8360", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "2928,3030,3129,3229,3336,3442,3563,8456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\da08fd731f7919b6d9fa1f8cb3c3a591\\transformed\\jetified-play-services-base-18.5.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3568,3673,3836,3964,4072,4240,4368,4490,4744,4932,5040,5210,5341,5500,5678,5746,5815", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "3668,3831,3959,4067,4235,4363,4485,4594,4927,5035,5205,5336,5495,5673,5741,5810,5897"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6250b576436e3aba2e4229fd698d3169\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4599", "endColumns": "144", "endOffsets": "4739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\43f536242e7d41c04ef9d5915cc2edb1\\transformed\\browser-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "56,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6088,6387,6486,6598", "endColumns": "115,98,111,102", "endOffsets": "6199,6481,6593,6696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\adc8160a94990199883d6d9f87c954dd\\transformed\\appcompat-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,8274", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,8355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\05840cd26434a51e94c2f67f7d2b7d17\\transformed\\preference-1.2.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,493,662,749", "endColumns": "69,87,78,150,168,86,80", "endOffsets": "170,258,337,488,657,744,825"}, "to": {"startLines": "55,58,72,73,76,77,78", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6018,6299,8044,8123,8461,8630,8717", "endColumns": "69,87,78,150,168,86,80", "endOffsets": "6083,6382,8118,8269,8625,8712,8793"}}]}]}