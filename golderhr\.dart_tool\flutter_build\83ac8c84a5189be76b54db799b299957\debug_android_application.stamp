{"inputs": ["E:\\Gordel_HR\\golderhr\\.dart_tool\\flutter_build\\83ac8c84a5189be76b54db799b299957\\app.dill", "E:\\Download\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "E:\\Download\\flutter\\bin\\cache\\engine.stamp", "E:\\Download\\flutter\\bin\\cache\\engine.stamp", "E:\\Download\\flutter\\bin\\cache\\engine.stamp", "E:\\Download\\flutter\\bin\\cache\\engine.stamp", "E:\\Gordel_HR\\golderhr\\pubspec.yaml", "E:\\Gordel_HR\\golderhr\\.env", "E:\\Gordel_HR\\golderhr\\assets\\images\\default_avatar.png", "E:\\Gordel_HR\\golderhr\\assets\\images\\logo.jpg", "E:\\Gordel_HR\\golderhr\\assets\\images\\no_notification.svg", "E:\\Gordel_HR\\golderhr\\assets\\images\\splash.jpg", "E:\\Gordel_HR\\golderhr\\assets\\images\\splash2.png", "E:\\Gordel_HR\\golderhr\\assets\\fonts\\Roboto-Black.ttf", "E:\\Gordel_HR\\golderhr\\assets\\fonts\\Roboto-Bold.ttf", "E:\\Gordel_HR\\golderhr\\assets\\fonts\\Roboto-Italic.ttf", "E:\\Gordel_HR\\golderhr\\assets\\fonts\\Roboto-Light.ttf", "E:\\Gordel_HR\\golderhr\\assets\\fonts\\Roboto-Medium.ttf", "E:\\Gordel_HR\\golderhr\\assets\\fonts\\Roboto-Regular.ttf", "E:\\flutter_pub_cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "E:\\flutter_pub_cache\\hosted\\pub.dev\\iconsax-0.0.8\\lib\\assets\\fonts\\iconsax.ttf", "E:\\flutter_pub_cache\\hosted\\pub.dev\\syncfusion_flutter_datagrid-28.2.12\\assets\\font\\UnsortIcon.ttf", "E:\\flutter_pub_cache\\hosted\\pub.dev\\syncfusion_flutter_datagrid-28.2.12\\assets\\font\\FilterIcon.ttf", "E:\\Download\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "E:\\Download\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "E:\\Gordel_HR\\golderhr\\.dart_tool\\flutter_build\\83ac8c84a5189be76b54db799b299957\\native_assets.json", "E:\\Download\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "E:\\Download\\flutter\\packages\\flutter\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\_flutterfire_internals-1.3.56\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\ansicolor-2.0.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\archive-4.0.7\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\args-2.7.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\async-2.13.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\bloc-9.0.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\cached_network_image-3.4.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\cached_network_image_platform_interface-4.1.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\cached_network_image_web-1.3.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\checked_yaml-2.0.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\cli_util-0.4.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\connectivity_plus-6.1.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\connectivity_plus_platform_interface-2.0.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\csslib-1.0.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\dartz-0.10.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\dbus-0.7.11\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\device_frame-1.3.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\device_info_plus-11.5.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\device_info_plus_platform_interface-7.0.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\device_preview-1.2.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\dio-5.8.0+1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\dio_web_adapter-2.1.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\dotted_border-3.1.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\equatable-2.0.7\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\file_selector_linux-0.9.3+2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\file_selector_macos-0.9.4+3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\file_selector_platform_interface-2.6.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\file_selector_windows-0.9.3+4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\firebase_core-3.14.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\firebase_core_platform_interface-5.4.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\firebase_core_web-2.23.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\firebase_messaging-15.2.7\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\firebase_messaging_platform_interface-4.6.7\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\firebase_messaging_web-3.10.7\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_animate-4.5.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_bloc-9.1.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_cache_manager-3.4.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_dotenv-5.2.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_launcher_icons-0.14.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_local_notifications-19.3.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_local_notifications_linux-6.0.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_local_notifications_platform_interface-9.1.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_local_notifications_windows-1.0.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_native_splash-2.4.6\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.28\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_secure_storage-9.2.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_secure_storage_linux-1.2.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_secure_storage_macos-3.1.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_secure_storage_platform_interface-1.1.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_secure_storage_web-1.2.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_secure_storage_windows-3.1.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_shaders-0.1.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\flutter_svg-2.2.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\freezed_annotation-2.4.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\geocoding-3.0.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\geocoding_android-3.3.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\geocoding_ios-3.0.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\geocoding_platform_interface-3.2.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\geolocator-14.0.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\geolocator_android-5.0.1+1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\geolocator_apple-2.3.13\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\geolocator_platform_interface-4.2.6\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\geolocator_web-4.1.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\geolocator_windows-0.2.5\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\get-4.7.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\get_it-8.0.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\get_storage-2.1.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\go_router-15.2.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\google_mlkit_commons-0.11.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\google_mlkit_face_detection-0.13.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\google_nav_bar-5.0.7\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\html-0.15.6\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\http-1.4.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\iconsax-0.0.8\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\image-4.5.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\image_picker-1.1.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\image_picker_android-0.8.12+23\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\image_picker_for_web-3.0.6\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\image_picker_ios-0.8.12+2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\image_picker_linux-0.2.1+2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\image_picker_macos-0.2.1+2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\image_picker_platform_interface-2.10.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\image_picker_windows-0.2.1+1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\intl-0.20.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\js-0.6.7\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\json_annotation-4.9.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\local_auth-2.3.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\local_auth_android-1.0.49\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\local_auth_darwin-1.4.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\local_auth_platform_interface-1.0.10\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\local_auth_windows-1.0.11\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\logger-2.6.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\logging-1.3.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\mime-2.0.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\nested-1.0.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\nm-0.5.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\octo_image-2.1.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_parsing-1.1.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\permission_handler-12.0.0+1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\permission_handler_android-13.0.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\permission_handler_apple-9.4.7\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\permission_handler_platform_interface-4.3.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\petitparser-6.1.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\posix-6.0.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\provider-6.1.5\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\rxdart-0.28.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\simple_gesture_detector-0.2.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\sqflite-2.4.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\sqflite_android-2.4.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\sqflite_common-2.5.5\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\sqflite_darwin-2.4.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\sqflite_platform_interface-2.4.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\syncfusion_flutter_core-28.2.12\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\syncfusion_flutter_datagrid-28.2.12\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\synchronized-3.4.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\table_calendar-3.2.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\timeago-3.7.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\timezone-0.10.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\universal_io-2.2.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\url_launcher-6.3.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\url_launcher_web-2.4.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\vector_graphics-1.1.19\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\vector_graphics_codec-1.1.13\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\vector_graphics_compiler-1.1.17\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\win32-5.14.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\win32_registry-2.1.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\xml-6.5.0\\LICENSE", "E:\\flutter_pub_cache\\hosted\\pub.dev\\yaml-3.1.3\\LICENSE", "E:\\Gordel_HR\\golderhr\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD628887051"], "outputs": ["E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\.env", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/default_avatar.png", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/logo.jpg", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/no_notification.svg", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/splash.jpg", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/splash2.png", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/Roboto-Black.ttf", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/Roboto-Bold.ttf", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/Roboto-Italic.ttf", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/Roboto-Light.ttf", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/Roboto-Medium.ttf", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/Roboto-Regular.ttf", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/iconsax/lib/assets/fonts/iconsax.ttf", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/syncfusion_flutter_datagrid/assets/font/UnsortIcon.ttf", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/syncfusion_flutter_datagrid/assets/font/FilterIcon.ttf", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "E:\\Gordel_HR\\golderhr\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]}