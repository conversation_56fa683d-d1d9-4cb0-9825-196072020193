﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\2e824e0c09e05f7498ba2a048bd61978\nuget-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\2e824e0c09e05f7498ba2a048bd61978\nuget-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\2e824e0c09e05f7498ba2a048bd61978\nuget-populate-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\2e824e0c09e05f7498ba2a048bd61978\nuget-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\2e824e0c09e05f7498ba2a048bd61978\nuget-populate-copyfile.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\2e824e0c09e05f7498ba2a048bd61978\nuget-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\2e824e0c09e05f7498ba2a048bd61978\nuget-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\2e824e0c09e05f7498ba2a048bd61978\nuget-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\2e824e0c09e05f7498ba2a048bd61978\nuget-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\5c0ee3ad85141572f76b47797c251667\nuget-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\c287be55502c7189bbd304f8e4d78df4\nuget-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\Gordel_HR\golderhr\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\nuget-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{BE76290A-3E72-3C25-9449-CA0840108841}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
