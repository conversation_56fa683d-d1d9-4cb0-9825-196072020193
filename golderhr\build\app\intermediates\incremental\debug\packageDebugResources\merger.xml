<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\android\app\src\main\res"><file name="background" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable\background.png" qualifiers="" type="drawable"/><file name="launch_background" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable\launch_background.xml" qualifiers="" type="drawable"/><file name="android12splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-hdpi\android12splash.png" qualifiers="hdpi-v4" type="drawable"/><file name="splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-hdpi\splash.png" qualifiers="hdpi-v4" type="drawable"/><file name="android12splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-mdpi\android12splash.png" qualifiers="mdpi-v4" type="drawable"/><file name="splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-mdpi\splash.png" qualifiers="mdpi-v4" type="drawable"/><file name="android12splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-night-hdpi\android12splash.png" qualifiers="night-hdpi-v8" type="drawable"/><file name="android12splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-night-mdpi\android12splash.png" qualifiers="night-mdpi-v8" type="drawable"/><file name="android12splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-night-xhdpi\android12splash.png" qualifiers="night-xhdpi-v8" type="drawable"/><file name="android12splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-night-xxhdpi\android12splash.png" qualifiers="night-xxhdpi-v8" type="drawable"/><file name="android12splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-night-xxxhdpi\android12splash.png" qualifiers="night-xxxhdpi-v8" type="drawable"/><file name="background" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-v21\background.png" qualifiers="v21" type="drawable"/><file name="launch_background" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-v21\launch_background.xml" qualifiers="v21" type="drawable"/><file name="android12splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-xhdpi\android12splash.png" qualifiers="xhdpi-v4" type="drawable"/><file name="splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-xhdpi\splash.png" qualifiers="xhdpi-v4" type="drawable"/><file name="android12splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-xxhdpi\android12splash.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-xxhdpi\splash.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="android12splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-xxxhdpi\android12splash.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="splash" path="E:\Gordel_HR\golderhr\android\app\src\main\res\drawable-xxxhdpi\splash.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="E:\Gordel_HR\golderhr\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Gordel_HR\golderhr\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Gordel_HR\golderhr\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Gordel_HR\golderhr\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Gordel_HR\golderhr\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="E:\Gordel_HR\golderhr\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="E:\Gordel_HR\golderhr\android\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="E:\Gordel_HR\golderhr\android\app\src\main\res\values-night-v31\styles.xml" qualifiers="night-v31"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:windowSplashScreenBackground">#ffffff</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/android12splash</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="E:\Gordel_HR\golderhr\android\app\src\main\res\values-v31\styles.xml" qualifiers="v31"><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:windowSplashScreenBackground">#ffffff</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/android12splash</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\app\generated\res\resValues\debug"/><source path="E:\Gordel_HR\golderhr\build\app\generated\res\google-services\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\app\generated\res\resValues\debug"/><source path="E:\Gordel_HR\golderhr\build\app\generated\res\google-services\debug"><file path="E:\Gordel_HR\golderhr\build\app\generated\res\google-services\debug\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">895106658180</string><string name="google_api_key" translatable="false">AIzaSyBye5nMBw14Pmxt9oa2hYwHdCD3ts75fdw</string><string name="google_app_id" translatable="false">1:895106658180:android:914f5c61af0e85b6f5f7d1</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBye5nMBw14Pmxt9oa2hYwHdCD3ts75fdw</string><string name="google_storage_bucket" translatable="false">goldenhr-54cda.firebasestorage.app</string><string name="project_id" translatable="false">goldenhr-54cda</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="legacy_api_res" generated-set="legacy_api_res$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>